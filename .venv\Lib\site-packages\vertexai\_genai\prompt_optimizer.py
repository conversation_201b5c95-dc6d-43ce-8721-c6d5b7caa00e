# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import datetime
import json
import logging
from typing import Any, Optional, Union
from urllib.parse import urlencode

from google.cloud import aiplatform
from google.genai import _api_module
from google.genai import _common
from google.genai import types as genai_types
from google.genai._common import get_value_by_path as getv
from google.genai._common import set_value_by_path as setv

from . import types


logger = logging.getLogger("vertexai_genai.promptoptimizer")


def _OptimizeRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["config"]) is not None:
        setv(to_object, ["config"], getv(from_object, ["config"]))

    return to_object


def _OptimizeResponse_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    return to_object


class PromptOptimizer(_api_module.BaseModule):
    """Prompt Optimizer"""

    def optimize_dummy(
        self, *, config: Optional[types.OptimizeConfigOrDict] = None
    ) -> types.OptimizeResponse:
        """Optimiza a multiple prompts."""

        parameter_model = types._OptimizeRequestParameters(
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _OptimizeRequestParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = ":optimize".format_map(request_url_dict)
            else:
                path = ":optimize"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[genai_types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("post", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _OptimizeResponse_from_vertex(response_dict)

        return_value = types.OptimizeResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )
        self._api_client._verify_response(return_value)

        return return_value

    """Prompt Optimizer PO-Data."""

    def _create_custom_job(
        self,
        display_name: str,
        container_uri: str,
        bucket: str,
        container_args: dict[str, str],
        service_account: str,
    ) -> aiplatform.CustomJob:
        """Create a custom jobs."""
        args = ["--%s=%s" % (k, v) for k, v in container_args.items()]
        worker_pool_specs = [
            {
                "replica_count": 1,
                "container_spec": {
                    "image_uri": container_uri,
                    "args": args,
                },
                "machine_spec": {
                    "machine_type": "n1-standard-4",
                },
            }
        ]

        custom_job = aiplatform.CustomJob(
            display_name=display_name,
            worker_pool_specs=worker_pool_specs,
            staging_bucket=bucket,
        )
        custom_job.submit(service_account=service_account)
        return custom_job

    def optimize(
        self,
        method: str,
        config: types.PromptOptimizerVAPOConfig,
    ) -> aiplatform.CustomJob:
        """Call PO-Data optimizer.

        Args:
          method: The method for optimizing multiple prompts.
          config: The config to use. Config  consists of the following fields: -
            config_path: The gcs path to the config file, e.g.
            gs://bucket/config.json. - wait_for_completion: Optional. Whether to
              wait for the job to complete. Default is True.
        """

        if method != "vapo":
            raise ValueError("Only vapo methods is currently supported.")

        timestamp = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
        display_name = f"vapo-optimizer-{timestamp}"
        wait_for_completion = config["wait_for_completion"]
        bucket = "/".join(config["config_path"].split("/")[:-1])

        container_uri = "us-docker.pkg.dev/vertex-ai/cair/vaipo:preview_v1_0"

        region = self._api_client.location
        project = self._api_client.project
        project_number = aiplatform.utils.resource_manager_utils.get_project_number(
            project
        )
        service_account = f"{project_number}-<EMAIL>"

        job = self._create_custom_job(
            display_name,
            container_uri,
            bucket,
            {
                "config": config["config_path"],
            },
            service_account,
        )

        # Get the job resource name
        job_resource_name = job.resource_name
        job_id = job_resource_name.split("/")[-1]
        logger.info("Job created: %s", job.resource_name)

        # Construct the dashboard URL
        dashboard_url = f"https://pantheon.corp.google.com/vertex-ai/locations/{region}/training/{job_id}/cpu?e=********&project={project}"
        logger.info("View the job status at: %s", dashboard_url)

        if wait_for_completion:
            logger.info("Waiting for the job to finish: %s", job.display_name)
            job.wait_for_completion()
        return job


class AsyncPromptOptimizer(_api_module.BaseModule):
    """Prompt Optimizer"""

    async def optimize_dummy(
        self, *, config: Optional[types.OptimizeConfigOrDict] = None
    ) -> types.OptimizeResponse:
        """Optimiza a multiple prompts."""

        parameter_model = types._OptimizeRequestParameters(
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _OptimizeRequestParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = ":optimize".format_map(request_url_dict)
            else:
                path = ":optimize"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[genai_types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "post", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _OptimizeResponse_from_vertex(response_dict)

        return_value = types.OptimizeResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )
        self._api_client._verify_response(return_value)

        return return_value
