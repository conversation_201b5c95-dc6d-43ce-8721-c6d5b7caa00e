import streamlit as st
import os
from datetime import datetime
from typing import List, Dict, Any, Optional

# Import Vertex AI and authentication
import vertexai
from google.oauth2 import service_account
from vertexai import agent_engines

# Configuration
USER_ID = "test_user"  # Hardcoded for now
# TODO: Replace with your actual resource ID from remote.py --create
RESOURCE_ID = ""  # Update this with output from remote.py --create

def initialize_vertex_ai():
    """Initialize Vertex AI with service account credentials from secrets."""
    try:
        # Get credentials from Streamlit secrets
        credentials = service_account.Credentials.from_service_account_info(
            st.secrets["gcp_service_account"]
        )

        # Initialize Vertex AI
        vertexai.init(
            project=st.secrets["gcp_service_account"]["project_id"],
            location="us-central1",  # Update if your location is different
            staging_bucket=f"gs://{st.secrets['gcp_service_account']['project_id']}-staging"
        )

        return True
    except KeyError as e:
        st.error(f"Missing secret key: {e}. Please configure .streamlit/secrets.toml.")
        return False
    except Exception as e:
        st.error(f"Error initializing Vertex AI: {e}")
        return False

# Direct functions using agent_engines (same as remote.py)
def create_new_session(resource_id: str, user_id: str) -> Optional[str]:
    """Create a new session and return session ID."""
    try:
        remote_app = agent_engines.get(resource_id)
        remote_session = remote_app.create_session(user_id=user_id)

        if isinstance(remote_session, str):
            return remote_session
        elif isinstance(remote_session, dict):
            return remote_session.get('id')
        else:
            return None
    except Exception as e:
        st.error(f"Error creating session: {e}")
        return None

def get_sessions_list(resource_id: str, user_id: str) -> List[Dict[str, Any]]:
    """Get list of sessions for the user."""
    try:
        remote_app = agent_engines.get(resource_id)
        sessions = remote_app.list_sessions(user_id=user_id)

        if isinstance(sessions, dict) and 'sessions' in sessions:
            return sessions['sessions']
        elif isinstance(sessions, list):
            return sessions
        else:
            return []
    except Exception as e:
        st.error(f"Error fetching sessions: {e}")
        return []

def get_session_details(resource_id: str, user_id: str, session_id: str) -> Optional[Dict[str, Any]]:
    """Get session details including conversation history."""
    try:
        remote_app = agent_engines.get(resource_id)
        session = remote_app.get_session(user_id=user_id, session_id=session_id)
        
        if isinstance(session, dict):
            return session
        else:
            return None
    except Exception as e:
        st.error(f"Error fetching session details: {e}")
        return None

def delete_session_by_id(resource_id: str, user_id: str, session_id: str) -> bool:
    """Delete a session by ID."""
    try:
        remote_app = agent_engines.get(resource_id)
        remote_app.delete_session(user_id=user_id, session_id=session_id)
        return True
    except Exception as e:
        st.error(f"Error deleting session: {e}")
        return False

def send_message_to_agent(resource_id: str, user_id: str, session_id: str, message: str) -> List[str]:
    """Send message to agent and return responses."""
    try:
        remote_app = agent_engines.get(resource_id)
        responses = []
        
        for event in remote_app.stream_query(
            user_id=user_id,
            session_id=session_id,
            message=message,
        ):
            responses.append(str(event))
        
        return responses
    except Exception as e:
        st.error(f"Error sending message: {e}")
        return []

def display_conversation_history(session_details: Dict[str, Any]):
    """Display conversation history from session events."""
    events = session_details.get('events', [])

    if not events:
        st.info("No conversation history yet. Start by sending a message!")
        return

    # Display events as conversation
    for i, event in enumerate(events):
        event_str = str(event)

        # Try to determine if it's a user message or agent response
        # This is a simple heuristic - you may need to adjust based on your event format
        if i % 2 == 0:  # Assume alternating pattern
            with st.chat_message("user"):
                st.write(event_str)
        else:
            with st.chat_message("assistant"):
                st.write(event_str)

def main():
    st.set_page_config(
        page_title="Vertex AI Agent Chat",
        page_icon="🤖",
        layout="wide"
    )
    
    st.title("🤖 Vertex AI Agent Chat")
    st.markdown("Chat with your deployed Vertex AI Agent Engine")
    
    # Initialize Vertex AI
    if not initialize_vertex_ai():
        st.stop()
    
    # Check if resource ID is configured
    if RESOURCE_ID == "your-resource-id-here":
        st.error("⚠️ Please update RESOURCE_ID in app.py with your actual resource ID from remote.py --create")
        st.info("Run `python remote.py --create` to deploy your agent and get the resource ID.")
        st.stop()
    
    # Initialize session state
    if "session_id" not in st.session_state:
        st.session_state.session_id = None
    if "sessions" not in st.session_state:
        st.session_state.sessions = []
    if "refresh_sessions" not in st.session_state:
        st.session_state.refresh_sessions = True
    
    # Sidebar for session management
    with st.sidebar:
        st.header("Session Management")
        
        # Refresh sessions button
        if st.button("🔄 Refresh Sessions"):
            st.session_state.refresh_sessions = True
        
        # Load sessions
        if st.session_state.refresh_sessions:
            st.session_state.sessions = get_sessions_list(RESOURCE_ID, USER_ID)
            st.session_state.refresh_sessions = False
        
        # Create new session
        if st.button("➕ Create New Session"):
            new_session_id = create_new_session(RESOURCE_ID, USER_ID)
            if new_session_id:
                st.success(f"Created new session: {new_session_id}")
                st.session_state.session_id = new_session_id
                st.session_state.refresh_sessions = True
                st.rerun()
        
        # Display sessions
        if st.session_state.sessions:
            st.subheader("Available Sessions")
            
            for i, session in enumerate(st.session_state.sessions):
                session_id = session.get('id', f'session_{i}')
                last_update = session.get('lastUpdateTime', 0)
                
                if last_update:
                    last_update_str = datetime.fromtimestamp(last_update).strftime('%m/%d %H:%M')
                else:
                    last_update_str = "Unknown"
                
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    if st.button(f"📝 {session_id[:8]}... ({last_update_str})", key=f"select_{session_id}"):
                        st.session_state.session_id = session_id
                        st.rerun()
                
                with col2:
                    if st.button("🗑️", key=f"delete_{session_id}", help="Delete session"):
                        if delete_session_by_id(RESOURCE_ID, USER_ID, session_id):
                            st.success("Session deleted!")
                            if st.session_state.session_id == session_id:
                                st.session_state.session_id = None
                            st.session_state.refresh_sessions = True
                            st.rerun()
        else:
            st.info("No sessions found. Create a new session to start chatting!")
    
    # Main chat interface
    if st.session_state.session_id:
        st.subheader(f"Chat Session: {st.session_state.session_id}")
        
        # Get and display session details
        session_details = get_session_details(RESOURCE_ID, USER_ID, st.session_state.session_id)
        
        if session_details:
            # Display conversation history
            display_conversation_history(session_details)
            
            # Chat input
            st.markdown("---")
            user_message = st.chat_input("Type your message here...")
            
            if user_message:
                # Display user message
                with st.chat_message("user"):
                    st.write(user_message)
                
                # Send message and get response
                with st.chat_message("assistant"):
                    with st.spinner("Thinking..."):
                        responses = send_message_to_agent(RESOURCE_ID, USER_ID, st.session_state.session_id, user_message)
                    
                    if responses:
                        for response in responses:
                            st.write(response)
                    else:
                        st.error("No response received from the agent.")
                
                # Refresh to show updated conversation
                st.rerun()
        else:
            st.error("Could not load session details. Please try refreshing or creating a new session.")
    else:
        st.info("👈 Please select or create a session from the sidebar to start chatting.")
        
        # Display some helpful information
        st.markdown("""
        ### Getting Started
        
        1. **Create a Session**: Click "Create New Session" in the sidebar
        2. **Start Chatting**: Select a session and type your message
        3. **Manage Sessions**: View, select, or delete sessions from the sidebar
        
        ### Configuration
        
        - **User ID**: `{}`
        - **Resource ID**: `{}`
        
        Make sure your Vertex AI Agent Engine is deployed and the Resource ID is correct.
        """.format(USER_ID, RESOURCE_ID))

if __name__ == "__main__":
    main()
