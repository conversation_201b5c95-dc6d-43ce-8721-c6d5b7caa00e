#!/usr/bin/env python3
"""
Test script to verify the Vertex AI Agent Engine setup
"""

import os
import sys
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        import streamlit
        print("✅ streamlit imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import streamlit: {e}")
        return False
    
    try:
        import vertexai
        print("✅ vertexai imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import vertexai: {e}")
        return False
    
    try:
        from vertexai import agent_engines
        print("✅ agent_engines imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import agent_engines: {e}")
        print("💡 Try: pip install 'google-cloud-aiplatform[adk,agent_engines]'")
        return False
    
    try:
        from google.oauth2 import service_account
        print("✅ service_account imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import service_account: {e}")
        return False
    
    try:
        from adk_gpa.agent import root_agent
        print("✅ root_agent imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import root_agent: {e}")
        print("💡 Make sure adk_gpa directory exists and contains agent.py")
        return False
    
    return True

def test_remote_functions():
    """Test if remote.py functions can be imported."""
    print("\n🔧 Testing remote.py functions...")
    
    try:
        from remote import create_session, list_sessions, get_session, delete_session, send_message
        print("✅ All remote.py functions imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import remote.py functions: {e}")
        print("💡 Make sure remote.py is in the same directory and contains all required functions")
        return False

def test_secrets_file():
    """Test if secrets file exists and has required structure."""
    print("\n🔐 Testing secrets configuration...")
    
    secrets_path = Path(".streamlit/secrets.toml")
    
    if not secrets_path.exists():
        print("❌ .streamlit/secrets.toml not found")
        print("💡 Run: python setup_env.py to create template")
        return False
    
    try:
        with open(secrets_path, 'r') as f:
            content = f.read()
        
        required_keys = [
            "gcp_service_account",
            "project_id",
            "private_key",
            "client_email"
        ]
        
        missing_keys = []
        for key in required_keys:
            if key not in content:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"❌ Missing keys in secrets.toml: {missing_keys}")
            return False
        
        if "your-project-id" in content or "YOUR_PRIVATE_KEY" in content:
            print("⚠️  secrets.toml contains placeholder values")
            print("💡 Update with your actual service account credentials")
            return False
        
        print("✅ secrets.toml exists and appears configured")
        return True
        
    except Exception as e:
        print(f"❌ Error reading secrets.toml: {e}")
        return False

def test_app_configuration():
    """Test if app.py is properly configured."""
    print("\n⚙️  Testing app.py configuration...")
    
    app_path = Path("app.py")
    
    if not app_path.exists():
        print("❌ app.py not found")
        return False
    
    try:
        with open(app_path, 'r') as f:
            content = f.read()
        
        if 'RESOURCE_ID = "your-resource-id-here"' in content:
            print("⚠️  RESOURCE_ID not configured in app.py")
            print("💡 Deploy your agent first: python remote.py --create")
            print("💡 Then update RESOURCE_ID in app.py")
            return False
        
        print("✅ app.py appears to be configured")
        return True
        
    except Exception as e:
        print(f"❌ Error reading app.py: {e}")
        return False

def test_environment_variables():
    """Test environment variables for remote.py."""
    print("\n🌍 Testing environment variables...")
    
    required_vars = [
        "GOOGLE_CLOUD_PROJECT",
        "GOOGLE_CLOUD_LOCATION", 
        "GOOGLE_CLOUD_STAGING_BUCKET"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  Missing environment variables: {missing_vars}")
        print("💡 Set these in your .env file or shell environment")
        return False
    
    print("✅ All required environment variables are set")
    return True

def main():
    """Run all tests."""
    print("🧪 Testing Vertex AI Agent Engine Setup")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_remote_functions,
        test_secrets_file,
        test_app_configuration,
        test_environment_variables
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ All tests passed ({passed}/{total})")
        print("\n🚀 Your setup is ready!")
        print("   Run: streamlit run app.py")
    else:
        print(f"⚠️  {passed}/{total} tests passed")
        print("\n🔧 Please address the issues above before running the app.")
        sys.exit(1)

if __name__ == "__main__":
    main()
