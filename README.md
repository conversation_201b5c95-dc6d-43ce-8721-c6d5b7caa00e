# Vertex AI Agent Engine Streamlit App

A complete Streamlit application that integrates with Google Cloud Vertex AI Agent Engine, providing a web interface for managing chat sessions and interacting with your deployed AI agent.

## Features

- 🤖 **Chat Interface**: Interactive chat with your Vertex AI Agent
- 📝 **Session Management**: Create, list, select, and delete chat sessions
- 🔄 **Real-time Updates**: Live conversation history with server-side context management
- 🔐 **Secure Authentication**: Service account-based authentication
- 📱 **Responsive UI**: Clean, modern interface built with Streamlit

## Prerequisites

1. **Google Cloud Project** with Vertex AI API enabled
2. **Service Account** with appropriate permissions:
   - Vertex AI User
   - Storage Admin (for staging bucket)
3. **Deployed Agent Engine** (created via `remote.py --create`)

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Authentication

Create `.streamlit/secrets.toml` with your service account credentials:

```toml
[gcp_service_account]
type = "service_account"
project_id = "your-project-id"
private_key_id = "your-private-key-id"
private_key = "-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY\n-----END PRIVATE KEY-----\n"
client_email = "<EMAIL>"
client_id = "your-client-id"
auth_uri = "https://accounts.google.com/o/oauth2/auth"
token_uri = "https://oauth2.googleapis.com/token"
auth_provider_x509_cert_url = "https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com"
client_x509_cert_url = "https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com"
universe_domain = "googleapis.com"
```

### 3. Deploy Your Agent

First, deploy your agent using the remote.py script:

```bash
# Set environment variables
export GOOGLE_CLOUD_PROJECT="your-project-id"
export GOOGLE_CLOUD_LOCATION="us-central1"
export GOOGLE_CLOUD_STAGING_BUCKET="your-staging-bucket"

# Deploy the agent
python remote.py --create
```

Copy the Resource ID from the output.

### 4. Update Configuration

Edit `app.py` and replace the placeholder with your actual Resource ID:

```python
# TODO: Replace with your actual resource ID from remote.py --create
RESOURCE_ID = "your-actual-resource-id-here"
```

### 5. Run the Application

```bash
streamlit run app.py
```

The app will be available at `http://localhost:8501`

## Usage

1. **Create a Session**: Click "Create New Session" in the sidebar
2. **Start Chatting**: Select a session and type your message in the chat input
3. **View History**: Conversation history is automatically displayed and managed server-side
4. **Manage Sessions**: Use the sidebar to switch between sessions or delete old ones

## Deployment Options

### Streamlit Community Cloud

1. Push your code to GitHub
2. Connect your repository to [Streamlit Community Cloud](https://share.streamlit.io/)
3. Add your secrets in the Streamlit Cloud dashboard
4. Deploy!

### Google Cloud Run

1. Create a `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8080
CMD ["streamlit", "run", "app.py", "--server.port=8080", "--server.address=0.0.0.0"]
```

2. Build and deploy:

```bash
gcloud builds submit --tag gcr.io/YOUR_PROJECT_ID/vertex-ai-chat
gcloud run deploy vertex-ai-chat --image gcr.io/YOUR_PROJECT_ID/vertex-ai-chat --platform managed
```

## Troubleshooting

### ImportError: No module named 'vertexai.agent_engines'

**Solution**: Install the correct version with agent_engines support:

```bash
pip install "google-cloud-aiplatform[adk,agent_engines]>=1.60.0"
pip install "google-adk>=1.4.2"
```

### ImportError: cannot import name 'create_session' from 'remote'

**Cause**: The remote.py file is missing flag definitions.

**Solution**: The remote.py file has been updated with the required flag definitions. Make sure you're using the updated version.

### Authentication Errors

**Solutions**:
1. Verify your service account has the correct permissions
2. Check that your `.streamlit/secrets.toml` file is properly formatted
3. Ensure your service account key is valid and not expired

### "Please update RESOURCE_ID" Error

**Solution**: 
1. Run `python remote.py --create` to deploy your agent
2. Copy the Resource ID from the output
3. Update the `RESOURCE_ID` variable in `app.py`

### Session Creation Fails

**Possible causes**:
1. Invalid Resource ID
2. Agent not properly deployed
3. Insufficient permissions

**Solutions**:
1. Verify the Resource ID is correct
2. Redeploy the agent: `python remote.py --create`
3. Check service account permissions

### No Response from Agent

**Possible causes**:
1. Agent deployment issues
2. Network connectivity problems
3. Invalid session ID

**Solutions**:
1. Check agent deployment status
2. Verify network connectivity to Google Cloud
3. Create a new session and try again

## File Structure

```
st_app/
├── app.py                 # Main Streamlit application
├── remote.py             # Agent deployment and management functions
├── requirements.txt      # Python dependencies
├── README.md            # This file
├── .streamlit/
│   └── secrets.toml     # Authentication secrets
└── adk_gpa/
    ├── __init__.py
    ├── agent.py         # Agent definition
    └── prompt.py        # Agent instructions
```

## Environment Variables

For `remote.py` operations, set these environment variables:

- `GOOGLE_CLOUD_PROJECT`: Your Google Cloud Project ID
- `GOOGLE_CLOUD_LOCATION`: Region (default: us-central1)
- `GOOGLE_CLOUD_STAGING_BUCKET`: Staging bucket for deployments

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Verify all prerequisites are met
3. Ensure your Google Cloud project has the necessary APIs enabled
4. Check the Streamlit logs for detailed error messages

## License

This project is licensed under the MIT License.
