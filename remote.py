import os
import sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from datetime import datetime

import vertexai
from absl import app, flags
from dotenv import load_dotenv
from vertexai import agent_engines
from vertexai.preview import reasoning_engines

from adk_gpa.agent import root_agent

# Define command line flags
flags.DEFINE_string("project_id", None, "Google Cloud Project ID")
flags.DEFINE_string("location", "us-central1", "Google Cloud Location")
flags.DEFINE_string("bucket", None, "Google Cloud Staging Bucket")
flags.DEFINE_string("user_id", "test_user", "User ID for sessions")
flags.DEFINE_string("resource_id", None, "Resource ID of the deployed agent")
flags.DEFINE_string("session_id", None, "Session ID for operations")
flags.DEFINE_string("message", None, "Message to send to the agent")

# Action flags
flags.DEFINE_bool("create", False, "Create a new deployment")
flags.DEFINE_bool("delete", False, "Delete a deployment")
flags.DEFINE_bool("list", False, "List all deployments")
flags.DEFINE_bool("create_session", False, "Create a new session")
flags.DEFINE_bool("list_sessions", False, "List all sessions")
flags.DEFINE_bool("get_session", False, "Get session details")
flags.DEFINE_bool("delete_session", False, "Delete a session")
flags.DEFINE_bool("send", False, "Send a message to the agent")

FLAGS = flags.FLAGS

# Deploy
def create() -> None:
    """Creates a new deployment."""
    try:
        # First wrap the agent in AdkApp
        app = reasoning_engines.AdkApp(
            agent=root_agent,
            enable_tracing=True,
        )

        # Now deploy to Agent Engine
        remote_app = agent_engines.create(
            agent_engine=app,
            requirements=[
                "google-cloud-aiplatform[adk,agent_engines]",
            ],
            # extra_packages=["./adk_gpa"],
            extra_packages=[os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'adk_gpa'))],
        )
        print(f"Created remote app: {remote_app.resource_name}")
        print(f"Resource ID: {remote_app.resource_name.split('/')[-1]}")
        print("\nUse this resource ID for session operations.")
    except ImportError as e:
        print(f"Error importing required modules: {e}")
        print("Make sure you have installed: pip install google-cloud-aiplatform[adk,agent_engines]")
    except FileNotFoundError as e:
        print(f"Error: Could not find agent files: {e}")
        print("Make sure the adk_gpa directory exists and contains the agent definition.")
    except Exception as e:
        print(f"Error creating deployment: {e}")
        print("Please check your Google Cloud configuration and permissions.")

# Delete Deployment
def delete(resource_id: str) -> None:
    """Deletes an existing deployment."""
    remote_app = agent_engines.get(resource_id)
    remote_app.delete(force=True)
    print(f"Deleted remote app: {resource_id}")


def list_deployments() -> None:
    """Lists all deployments."""
    deployments = agent_engines.list()
    if not deployments:
        print("No deployments found.")
        return
    print("Deployments:")
    for deployment in deployments:
        print(f"- {deployment.resource_name}")


def create_session(resource_id: str, user_id: str) -> None:
    """Creates a new session for the specified user."""
    remote_app = agent_engines.get(resource_id)
    try:
        remote_session = remote_app.create_session(user_id=user_id)
        print("Created session:")

        # Handle case where session is a string (session ID) or a dict (session object)
        if isinstance(remote_session, str):
            print(f"  Session ID: {remote_session}")
        elif isinstance(remote_session, dict):
            print(f"  Session ID: {remote_session.get('id', 'N/A')}")
            print(f"  User ID: {remote_session.get('userId', 'N/A')}")
            print(f"  App name: {remote_session.get('appName', 'N/A')}")
            # print(f"  Last update time: {remote_session.get('lastUpdateTime', 'N/A')}")
            print(f"  Last update time: {datetime.fromtimestamp(remote_session.get('lastUpdateTime', 0)).strftime('%d-%m-%Y %H:%M:%S')}")
            print(f"  State: {remote_session.get('state', 'N/A')}")
            print(f"  Events: {remote_session.get('events', 'N/A')}")
            
        else:
            print(f"  Session: {remote_session}")

        print("\nUse this session ID with --session_id when sending messages.")
    except AttributeError:
        print(f"Error: create_session method not available on AgentEngine.")
        print("This might be because the agent was not deployed with the correct configuration.")
    except Exception as e:
        print(f"Error creating session: {e}")


def list_sessions(resource_id: str, user_id: str) -> None:
    """Lists all sessions for the specified user."""
    remote_app = agent_engines.get(resource_id)
    try:
        sessions = remote_app.list_sessions(user_id=user_id)
        print(f"Sessions for user '{user_id}': ")
        print()

        if not sessions:
            print("No sessions found.")
            return

        # Handle case where session is a string (session ID) or a dict (session object)
        if isinstance(sessions, str):
            print(f"- Session ID: {sessions}")
        elif isinstance(sessions, dict):
            for i, remote_session in enumerate(sessions.get('sessions', []), 1):
                print(f"Session {i}:")
                print(f"  Session ID: {remote_session.get('id', 'N/A')}")
                print(f"  User ID: {remote_session.get('userId', 'N/A')}")
                print(f"  App name: {remote_session.get('appName', 'N/A')}")
                # print(f"  Last update time: {remote_session.get('lastUpdateTime', 'N/A')}")
                print(f"  Last update time: {datetime.fromtimestamp(remote_session.get('lastUpdateTime', 0)).strftime('%d-%m-%Y %H:%M:%S')}")
                print(f"  State: {remote_session.get('state', 'N/A')}")
                # print(f"  Events: {remote_session.get('events', 'N/A')}")
                print()
        else:
            print(f"- Session: {sessions}")
                
    except AttributeError:
        print(f"Error: list_sessions method not available on AgentEngine.")
        print("This might be because the agent was not deployed with the correct configuration.")
    except Exception as e:
        print(f"Error listing sessions: {e}")


def get_session(resource_id: str, user_id: str, session_id: str) -> None:
    """Gets a specific session."""
    remote_app = agent_engines.get(resource_id)
    try:
        session = remote_app.get_session(user_id=user_id, session_id=session_id)
        print("Session details:")
        # Handle case where session is a string (session ID) or a dict (session object)
        if isinstance(session, str):
            print(f"  Session ID: {session}")
        elif isinstance(session, dict):
            print(f"  Session ID: {session.get('id', 'N/A')}")
            print(f"  User ID: {session.get('userId', 'N/A')}")
            print(f"  App name: {session.get('appName', 'N/A')}")
            # print(f"  Last update time: {session.get('lastUpdateTime', 'N/A')}")
            print(f"  Last update time: {datetime.fromtimestamp(session.get('lastUpdateTime', 0)).strftime('%d-%m-%Y %H:%M:%S')}")
            print(f"  State: {session.get('state', 'N/A')}")
            print(f"  Events: {session.get('events', 'N/A')}")
        else:
            print(f"  Session: {session}")
    except AttributeError:
        print(f"Error: get_session method not available on AgentEngine. Session ID: {session_id}")


def delete_session(resource_id: str, user_id: str, session_id: str) -> None:
    """Deletes a specific session."""
    remote_app = agent_engines.get(resource_id)
    try:
        remote_app.delete_session(user_id=user_id, session_id=session_id)
        print(f"Deleted session {session_id} for user {user_id}")
    except AttributeError:
        print(f"Error: delete_session method not available on AgentEngine. Session ID: {session_id}")


def send_message(resource_id: str, user_id: str, session_id: str, message: str) -> None:
    """Sends a message to the deployed agent."""
    remote_app = agent_engines.get(resource_id)

    print(f"Sending message to session {session_id}:")
    print(f"Message: {message}")
    print("\nResponse:")
    try:
        for event in remote_app.stream_query(
            user_id=user_id,
            session_id=session_id,
            message=message,
        ):
            print(event)
    except AttributeError:
        print("Error: stream_query method not available on AgentEngine.")
        print("This might be because the agent was not deployed with the correct configuration.")
        print("Please ensure your agent supports streaming queries.")


def main(argv=None):
    """Main function that can be called directly or through app.run()."""
    # Parse flags first
    if argv is None:
        argv = flags.FLAGS(sys.argv)
    else:
        argv = flags.FLAGS(argv)

    load_dotenv()

    # Now we can safely access the flags
    project_id = (
        FLAGS.project_id if FLAGS.project_id else os.getenv("GOOGLE_CLOUD_PROJECT")
    )
    location = FLAGS.location if FLAGS.location else os.getenv("GOOGLE_CLOUD_LOCATION")
    bucket = FLAGS.bucket if FLAGS.bucket else os.getenv("GOOGLE_CLOUD_STAGING_BUCKET")
    user_id = FLAGS.user_id

    if not project_id:
        print("Missing required environment variable: GOOGLE_CLOUD_PROJECT")
        return
    elif not location:
        print("Missing required environment variable: GOOGLE_CLOUD_LOCATION")
        return
    elif not bucket:
        print("Missing required environment variable: GOOGLE_CLOUD_STAGING_BUCKET")
        return

    vertexai.init(
        project=project_id,
        location=location,
        staging_bucket=bucket,
    )

    if FLAGS.create:
        create()
    elif FLAGS.delete:
        if not FLAGS.resource_id:
            print("resource_id is required for delete")
            return
        delete(FLAGS.resource_id)
    elif FLAGS.list:
        list_deployments()
    elif FLAGS.create_session:
        if not FLAGS.resource_id:
            print("resource_id is required for create_session")
            return
        create_session(FLAGS.resource_id, user_id)
    elif FLAGS.list_sessions:
        if not FLAGS.resource_id:
            print("resource_id is required for list_sessions")
            return
        list_sessions(FLAGS.resource_id, user_id)
    elif FLAGS.get_session:
        if not FLAGS.resource_id:
            print("resource_id is required for get_session")
            return
        if not FLAGS.session_id:
            print("session_id is required for get_session")
            return
        get_session(FLAGS.resource_id, user_id, FLAGS.session_id)
    elif FLAGS.delete_session:
        if not FLAGS.resource_id:
            print("resource_id is required for delete_session")
            return
        if not FLAGS.session_id:
            print("session_id is required for delete_session")
            return
        delete_session(FLAGS.resource_id, user_id, FLAGS.session_id)
    elif FLAGS.send:
        if not FLAGS.resource_id:
            print("resource_id is required for send")
            return
        if not FLAGS.session_id:
            print("session_id is required for send")
            return
        send_message(FLAGS.resource_id, user_id, FLAGS.session_id, FLAGS.message)
    else:
        print(
            "Please specify one of: --create, --delete, --list, --create_session, --list_sessions, --get_session, --delete_session, or --send"
        )


if __name__ == "__main__":
    app.run(main)
