#!/usr/bin/env python3
"""
Environment setup script for Vertex AI Agent Engine Streamlit App
"""

import os
import json
import sys
from pathlib import Path

def create_env_file():
    """Create a .env file with required environment variables."""
    env_content = """# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_CLOUD_STAGING_BUCKET=your-staging-bucket

# Optional: Path to service account key file
# GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json
"""
    
    env_path = Path(".env")
    if env_path.exists():
        print("⚠️  .env file already exists. Skipping creation.")
        return
    
    with open(env_path, "w") as f:
        f.write(env_content)
    
    print("✅ Created .env file. Please update it with your actual values.")

def create_secrets_template():
    """Create a template for Streamlit secrets."""
    secrets_dir = Path(".streamlit")
    secrets_dir.mkdir(exist_ok=True)
    
    secrets_path = secrets_dir / "secrets.toml"
    
    if secrets_path.exists():
        print("⚠️  .streamlit/secrets.toml already exists. Skipping creation.")
        return
    
    secrets_content = """[gcp_service_account]
type = "service_account"
project_id = "your-project-id"
private_key_id = "your-private-key-id"
private_key = "-----BEGIN PRIVATE KEY-----\\nYOUR_PRIVATE_KEY_HERE\\n-----END PRIVATE KEY-----\\n"
client_email = "<EMAIL>"
client_id = "your-client-id"
auth_uri = "https://accounts.google.com/o/oauth2/auth"
token_uri = "https://oauth2.googleapis.com/token"
auth_provider_x509_cert_url = "https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com"
client_x509_cert_url = "https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com"
universe_domain = "googleapis.com"
"""
    
    with open(secrets_path, "w") as f:
        f.write(secrets_content)
    
    print("✅ Created .streamlit/secrets.toml template. Please update it with your actual service account credentials.")

def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = [
        "streamlit",
        "google-cloud-aiplatform",
        "google-adk",
        "vertexai"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Install missing packages with:")
        print("   pip install -r requirements.txt")
        return False
    else:
        print("✅ All required packages are installed.")
        return True

def validate_gcp_setup():
    """Validate Google Cloud setup."""
    project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
    location = os.getenv("GOOGLE_CLOUD_LOCATION")
    bucket = os.getenv("GOOGLE_CLOUD_STAGING_BUCKET")
    
    issues = []
    
    if not project_id:
        issues.append("GOOGLE_CLOUD_PROJECT environment variable not set")
    
    if not location:
        issues.append("GOOGLE_CLOUD_LOCATION environment variable not set")
    
    if not bucket:
        issues.append("GOOGLE_CLOUD_STAGING_BUCKET environment variable not set")
    
    if issues:
        print("❌ Google Cloud configuration issues:")
        for issue in issues:
            print(f"   - {issue}")
        print("\n💡 Set these environment variables in your .env file or shell.")
        return False
    else:
        print("✅ Google Cloud environment variables are set.")
        return True

def main():
    """Main setup function."""
    print("🚀 Setting up Vertex AI Agent Engine Streamlit App")
    print("=" * 50)
    
    # Create configuration files
    create_env_file()
    create_secrets_template()
    
    print("\n📦 Checking dependencies...")
    deps_ok = check_dependencies()
    
    print("\n☁️  Checking Google Cloud configuration...")
    gcp_ok = validate_gcp_setup()
    
    print("\n" + "=" * 50)
    
    if deps_ok and gcp_ok:
        print("✅ Setup complete! Next steps:")
        print("   1. Update .env with your Google Cloud project details")
        print("   2. Update .streamlit/secrets.toml with your service account credentials")
        print("   3. Deploy your agent: python remote.py --create")
        print("   4. Update RESOURCE_ID in app.py")
        print("   5. Run the app: streamlit run app.py")
    else:
        print("⚠️  Setup incomplete. Please address the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
