import streamlit as st
from google.cloud import aiplatform
from google.oauth2 import service_account
from vertexai.preview import agent_engines
import os
from datetime import datetime
import sys

# Add parent directory to sys.path to import adk_gpa
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from adk_gpa.agent import root_agent
except ImportError as e:
    st.error(f"Failed to import root_agent from adk_gpa: {e}")
    st.stop()

# Initialize Vertex AI
def initialize_vertex_ai():
    try:
        credentials = service_account.Credentials.from_service_account_info(
            st.secrets["gcp_service_account"]
        )
        aiplatform.init(
            project=st.secrets["gcp_service_account"]["project_id"],
            location="us-central1",
            credentials=credentials,
            staging_bucket=f"gs://{st.secrets['gcp_service_account']['project_id']}-staging"
        )
    except KeyError as e:
        st.error(f"Missing secret key: {e}. Please configure secrets.toml.")
        st.stop()
    except Exception as e:
        st.error(f"Error initializing Vertex AI: {e}")
        st.stop()

# Create a new session
def create_session(resource_id, user_id):
    try:
        remote_app = agent_engines.get(resource_id)
        remote_session = remote_app.create_session(user_id=user_id)
        if isinstance(remote_session, dict):
            return remote_session.get('id')
        return remote_session
    except Exception as e:
        st.error(f"Error creating session: {e}")
        return None

# List sessions for a user
def list_sessions(resource_id, user_id):
    try:
        remote_app = agent_engines.get(resource_id)
        sessions = remote_app.list_sessions(user_id=user_id)
        if isinstance(sessions, dict):
            return sessions.get('sessions', [])
        return sessions
    except Exception as e:
        st.error(f"Error listing sessions: {e}")
        return []

# Get session details
def get_session(resource_id, user_id, session_id):
    try:
        remote_app = agent_engines.get(resource_id)
        session = remote_app.get_session(user_id=user_id, session_id=session_id)
        return session
    except Exception as e:
        st.error(f"Error getting session: {e}")
        return None

# Delete a session
def delete_session(resource_id, user_id, session_id):
    try:
        remote_app = agent_engines.get(resource_id)
        remote_app.delete_session(user_id=user_id, session_id=session_id)
        st.success(f"Deleted session {session_id}")
    except Exception as e:
        st.error(f"Error deleting session: {e}")

# Send a message to the agent
def send_message(resource_id, user_id, session_id, message):
    try:
        remote_app = agent_engines.get(resource_id)
        responses = []
        for event in remote_app.stream_query(user_id=user_id, session_id=session_id, message=message):
            responses.append(event)
        return responses
    except Exception as e:
        st.error(f"Error sending message: {e}")
        return []

# Streamlit app
def main():
    st.title("Vertex AI Agent Chat")

    # Initialize Vertex AI
    initialize_vertex_ai()

    # Replace with your deployed resource ID
    RESOURCE_ID = "your-resource-id"  # Obtain from remote.py create function
    USER_ID = "test_user"  # Replace with dynamic user ID if needed

    # Initialize session state
    if "session_id" not in st.session_state:
        st.session_state.session_id = None
    if "sessions" not in st.session_state:
        st.session_state.sessions = []

    # Sidebar for session management
    with st.sidebar:
        st.header("Chat Sessions")
        if st.button("Create New Chat"):
            new_session_id = create_session(RESOURCE_ID, USER_ID)
            if new_session_id:
                st.session_state.session_id = new_session_id
                st.session_state.sessions = list_sessions(RESOURCE_ID, USER_ID)
                st.success(f"Created new session: {new_session_id}")

        # List sessions
        st.session_state.sessions = list_sessions(RESOURCE_ID, USER_ID)
        if st.session_state.sessions:
            session_options = {
                f"Session {s.get('id', 'N/A')} ({datetime.fromtimestamp(s.get('lastUpdateTime', 0)).strftime('%d-%m-%Y %H:%M:%S')})": s.get('id')
                for s in st.session_state.sessions
            }
            selected_session = st.selectbox("Select a Chat", options=list(session_options.keys()))
            st.session_state.session_id = session_options.get(selected_session)

            # Delete session
            if st.button("Delete Selected Chat"):
                if st.session_state.session_id:
                    delete_session(RESOURCE_ID, USER_ID, st.session_state.session_id)
                    st.session_state.session_id = None
                    st.session_state.sessions = list_sessions(RESOURCE_ID, USER_ID)

    # Main chat interface
    if st.session_state.session_id:
        st.subheader(f"Chat Session: {st.session_state.session_id}")

        # Display chat history
        session = get_session(RESOURCE_ID, USER_ID, st.session_state.session_id)
        if session and isinstance(session, dict):
            events = session.get('events', [])
            for event in events:
                if event.get('role') == 'user':
                    with st.chat_message("user"):
                        st.write(event.get('content', ''))
                elif event.get('role') == 'assistant':
                    with st.chat_message("assistant"):
                        st.write(event.get('content', ''))

        # Chat input
        if prompt := st.chat_input("Type your message..."):
            with st.chat_message("user"):
                st.write(prompt)
            responses = send_message(RESOURCE_ID, USER_ID, st.session_state.session_id, prompt)
            for response in responses:
                with st.chat_message("assistant"):
                    st.write(response.get('content', ''))

    else:
        st.info("Create or select a chat session to start chatting.")

if __name__ == "__main__":
    main()